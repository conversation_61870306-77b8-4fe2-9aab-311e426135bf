'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useArticleStore } from '@/lib/stores/article-store';
import { Search, Plus, Edit, Eye, Trash2, RefreshCw, CheckSquare } from 'lucide-react';

export default function ArticlesPage() {
  const router = useRouter();
  const {
    articles,
    loading,
    error,
    searchTerm,
    statusFilter,
    currentPage,
    totalPages,
    total,
    setSearchTerm,
    setStatusFilter,
    setCurrentPage,
    fetchArticles,
    deleteArticle,
  } = useArticleStore();

  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [showBatchActions, setShowBatchActions] = useState(false);

  useEffect(() => {
    fetchArticles();
  }, [fetchArticles, currentPage, statusFilter]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setSearchTerm(localSearchTerm);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [localSearchTerm, setSearchTerm]);

  const handleDelete = async (id: string, title: string) => {
    if (window.confirm(`确定要删除文章 "${title}" 吗？此操作不可撤销。`)) {
      try {
        await deleteArticle(id);
      } catch {
        alert('删除失败，请重试');
      }
    }
  };

  const handleStatusFilterChange = (status: 'ALL' | 'DRAFT' | 'PUBLISHED') => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  // 批量操作相关函数
  const handleSelectAll = () => {
    if (selectedArticles.length === articles.length) {
      setSelectedArticles([]);
    } else {
      setSelectedArticles(articles.map(article => article.id));
    }
  };

  const handleSelectArticle = (articleId: string) => {
    setSelectedArticles(prev =>
      prev.includes(articleId)
        ? prev.filter(id => id !== articleId)
        : [...prev, articleId]
    );
  };

  const handleBatchDelete = async () => {
    if (selectedArticles.length === 0) return;

    const confirmed = window.confirm(
      `确定要删除选中的 ${selectedArticles.length} 篇文章吗？此操作不可撤销。`
    );

    if (confirmed) {
      try {
        // 批量删除
        await Promise.all(selectedArticles.map(id => deleteArticle(id)));
        setSelectedArticles([]);
        setShowBatchActions(false);
      } catch {
        alert('批量删除失败，请重试');
      }
    }
  };

  const handleBatchStatusUpdate = async (newStatus: 'DRAFT' | 'PUBLISHED') => {
    if (selectedArticles.length === 0) return;

    try {
      // 这里需要实现批量状态更新的API
      // 暂时显示提示
      alert(`批量更新状态功能开发中，将 ${selectedArticles.length} 篇文章状态更新为 ${newStatus === 'PUBLISHED' ? '已发布' : '草稿'}`);
      setSelectedArticles([]);
      setShowBatchActions(false);
    } catch {
      alert('批量状态更新失败，请重试');
    }
  };

  // 监听选中状态变化
  useEffect(() => {
    setShowBatchActions(selectedArticles.length > 0);
  }, [selectedArticles]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto p-6">
        {/* 页面标题和操作 */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-pink-400 bg-clip-text text-transparent">
              文章管理
            </h1>
            <p className="text-slate-400 mt-2">管理您的微信公众号文章</p>
            {total > 0 && (
              <p className="text-slate-500 text-sm mt-1">
                共 {total} 篇文章，第 {currentPage} / {totalPages} 页
              </p>
            )}
          </div>
          
          <div className="flex gap-2 mt-4 md:mt-0">
            <Button
              variant="outline"
              onClick={() => fetchArticles()}
              disabled={loading}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
            
            <Button 
              className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
              onClick={() => router.push('/admin/articles/new')}
            >
              <Plus className="w-4 h-4 mr-2" />
              新建文章
            </Button>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6 bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  placeholder="搜索文章标题或内容..."
                  value={localSearchTerm}
                  onChange={(e) => setLocalSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                />
              </div>

              <div className="flex gap-2">
                <Button
                  variant={statusFilter === 'ALL' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusFilterChange('ALL')}
                  className={statusFilter === 'ALL'
                    ? 'bg-cyan-600 text-white'
                    : 'border-slate-600 text-slate-300 hover:bg-slate-700'
                  }
                >
                  全部
                </Button>
                <Button
                  variant={statusFilter === 'DRAFT' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusFilterChange('DRAFT')}
                  className={statusFilter === 'DRAFT'
                    ? 'bg-yellow-600 text-white'
                    : 'border-slate-600 text-slate-300 hover:bg-slate-700'
                  }
                >
                  草稿
                </Button>
                <Button
                  variant={statusFilter === 'PUBLISHED' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusFilterChange('PUBLISHED')}
                  className={statusFilter === 'PUBLISHED'
                    ? 'bg-green-600 text-white'
                    : 'border-slate-600 text-slate-300 hover:bg-slate-700'
                  }
                >
                  已发布
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 批量操作栏 */}
        {showBatchActions && (
          <Card className="mb-6 bg-blue-900/20 border-blue-700">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CheckSquare className="w-5 h-5 text-blue-400" />
                  <span className="text-blue-400 font-medium">
                    已选择 {selectedArticles.length} 篇文章
                  </span>
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSelectAll}
                    className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
                  >
                    {selectedArticles.length === articles.length ? '取消全选' : '全选'}
                  </Button>

                  <Select onValueChange={(value: string) => handleBatchStatusUpdate(value as 'DRAFT' | 'PUBLISHED')}>
                    <SelectTrigger className="w-32 bg-slate-700 border-slate-600 text-white cursor-pointer">
                      <SelectValue placeholder="更新状态" />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      <SelectItem value="DRAFT" className="text-white cursor-pointer hover:bg-slate-600">
                        设为草稿
                      </SelectItem>
                      <SelectItem value="PUBLISHED" className="text-white cursor-pointer hover:bg-slate-600">
                        设为已发布
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBatchDelete}
                    className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white cursor-pointer"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    批量删除
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedArticles([])}
                    className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
                  >
                    取消选择
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 错误提示 */}
        {error && (
          <Card className="mb-6 bg-red-900/20 border-red-700">
            <CardContent className="p-4">
              <p className="text-red-400">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* 加载状态 */}
        {loading && (
          <div className="text-center py-8">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto text-cyan-400 mb-2" />
            <p className="text-slate-400">加载中...</p>
          </div>
        )}

        {/* 文章列表 */}
        {!loading && (
          <div className="grid gap-4">
            {articles.length === 0 ? (
              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-8 text-center">
                  <p className="text-slate-400 text-lg">暂无文章</p>
                  <p className="text-slate-500 mt-2">点击"新建文章"开始创建您的第一篇文章</p>
                </CardContent>
              </Card>
            ) : (
              articles.map((article) => (
                <Card key={article.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex items-start gap-3 flex-1">
                        <Checkbox
                          checked={selectedArticles.includes(article.id)}
                          onCheckedChange={() => handleSelectArticle(article.id)}
                          className="border-slate-400 mt-1 cursor-pointer"
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-semibold text-white">{article.title}</h3>
                          <Badge 
                            variant={article.status === 'PUBLISHED' ? 'default' : 'secondary'}
                            className={article.status === 'PUBLISHED' 
                              ? 'bg-green-600 text-white hover:bg-green-700' 
                              : 'bg-yellow-600 text-white hover:bg-yellow-700'
                            }
                          >
                            {article.status === 'PUBLISHED' ? '已发布' : '草稿'}
                          </Badge>
                        </div>

                          {article.excerpt && (
                            <p className="text-slate-300 mb-3 line-clamp-2">{article.excerpt}</p>
                          )}

                          <div className="flex items-center gap-4 text-sm text-slate-400">
                            <span>路径: /{article.slug}</span>
                            <span>更新: {new Date(article.updatedAt).toLocaleDateString('zh-CN')}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
                          onClick={() => router.push(`/posts/${article.slug}`)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
                          onClick={() => router.push(`/admin/articles/${article.id}/edit`)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white cursor-pointer"
                          onClick={() => handleDelete(article.id, article.title)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2 mt-8">
            <Button
              variant="outline"
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
              className="border-slate-600 text-slate-300 hover:bg-slate-700 disabled:opacity-50"
            >
              上一页
            </Button>
            
            <span className="flex items-center px-4 text-slate-400">
              {currentPage} / {totalPages}
            </span>
            
            <Button
              variant="outline"
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
              className="border-slate-600 text-slate-300 hover:bg-slate-700 disabled:opacity-50"
            >
              下一页
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
