'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useArticleStore } from '@/lib/stores/article-store';
import {
  FileText,
  Plus,
  Eye,
  Edit,
  Settings,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export default function AdminDashboard() {
  const { articles, loading } = useArticleStore();
  const [stats, setStats] = useState({
    total: 0,
    published: 0,
    drafts: 0,
    recent: 0,
  });

  useEffect(() => {
    if (articles) {
      const published = articles.filter(a => a.status === 'PUBLISHED').length;
      const drafts = articles.filter(a => a.status === 'DRAFT').length;
      const recent = articles.filter(a => {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return new Date(a.createdAt) > weekAgo;
      }).length;

      setStats({
        total: articles.length,
        published,
        drafts,
        recent,
      });
    }
  }, [articles]);

  const recentArticles = articles?.slice(0, 5) || [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto p-6 max-w-7xl">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-2">
            Silicon Based Teahouse 2077
          </h1>
          <p className="text-slate-300 text-lg">管理控制台</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">总文章数</p>
                  <p className="text-2xl font-bold text-white">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-cyan-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">已发布</p>
                  <p className="text-2xl font-bold text-green-400">{stats.published}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">草稿</p>
                  <p className="text-2xl font-bold text-yellow-400">{stats.drafts}</p>
                </div>
                <AlertCircle className="h-8 w-8 text-yellow-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">本周新增</p>
                  <p className="text-2xl font-bold text-purple-400">{stats.recent}</p>
                </div>
                <Clock className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 快速操作 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Plus className="h-5 w-5 text-cyan-400" />
                快速创建
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin/articles/new">
                <Button className="w-full bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 cursor-pointer">
                  <Plus className="h-4 w-4 mr-2" />
                  新建文章
                </Button>
              </Link>
              <Link href="/admin/articles/simple">
                <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer">
                  <Edit className="h-4 w-4 mr-2" />
                  简单编辑器
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <FileText className="h-5 w-5 text-cyan-400" />
                文章管理
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin/articles">
                <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer">
                  <FileText className="h-4 w-4 mr-2" />
                  所有文章
                </Button>
              </Link>
              <Link href="/tools">
                <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer">
                  <Settings className="h-4 w-4 mr-2" />
                  工具箱
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-cyan-400" />
                系统状态
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-slate-300">数据库</span>
                <span className="text-green-400">正常</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-300">编辑器</span>
                <span className="text-green-400">就绪</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-slate-300">WeChat组件</span>
                <span className="text-green-400">可用</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 最近文章 */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-cyan-400" />
                最近文章
              </span>
              <Link href="/admin/articles">
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer">
                  查看全部
                </Button>
              </Link>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin w-8 h-8 border-2 border-cyan-400 border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-slate-400">加载中...</p>
              </div>
            ) : recentArticles.length > 0 ? (
              <div className="space-y-3">
                {recentArticles.map((article) => (
                  <div key={article.id} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                    <div className="flex-1">
                      <h3 className="text-white font-medium">{article.title}</h3>
                      <p className="text-slate-400 text-sm">
                        {new Date(article.createdAt).toLocaleDateString()} • 
                        <span className={`ml-1 ${article.status === 'PUBLISHED' ? 'text-green-400' : 'text-yellow-400'}`}>
                          {article.status === 'PUBLISHED' ? '已发布' : '草稿'}
                        </span>
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Link href={`/posts/${article.slug}`}>
                        <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      <Link href={`/admin/articles/${article.id}/edit`}>
                        <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400 mb-4">还没有文章</p>
                <Link href="/admin/articles/new">
                  <Button className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 cursor-pointer">
                    创建第一篇文章
                  </Button>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
