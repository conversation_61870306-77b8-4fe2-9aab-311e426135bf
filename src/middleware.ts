import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const startTime = Date.now();
  
  // 创建响应
  const response = NextResponse.next();
  
  // 添加性能监控头
  response.headers.set('X-Request-Start', startTime.toString());
  response.headers.set('X-Request-ID', crypto.randomUUID());
  
  // 添加安全头
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // 在开发环境中添加调试信息
  if (process.env.NODE_ENV === 'development') {
    response.headers.set('X-Debug-Path', request.nextUrl.pathname);
    response.headers.set('X-Debug-Method', request.method);
  }
  
  return response;
}

export const config = {
  matcher: [
    /*
     * 匹配所有请求路径，除了：
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
